from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import sqlite3, os, re, io, base64
from datetime import datetime
import qrcode
from PIL import Image
import requests
import json

DB = "school_ops.sqlite"

app = Flask(__name__, static_url_path="/static", static_folder="static")
CORS(app)

SCHEMA = """
PRAGMA foreign_keys=ON;

CREATE TABLE IF NOT EXISTS students(
  id TEXT PRIMARY KEY,
  name TEXT
);

CREATE TABLE IF NOT EXISTS assignments(
  id TEXT PRIMARY KEY,
  title TEXT,
  due_date TEXT
);

-- 加 day 欄位做「每天唯一」以避免連掃重覆
CREATE TABLE IF NOT EXISTS submissions(
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  student_id TEXT,
  assignment_id TEXT,
  ts TEXT DEFAULT (datetime('now','localtime')),
  day TEXT DEFAULT (date('now','localtime')),
  status TEXT DEFAULT 'submitted',
  UNIQUE(student_id, assignment_id, day) ON CONFLICT IGNORE
);

CREATE TABLE IF NOT EXISTS items(
  id TEXT PRIMARY KEY,
  name TEXT
);

CREATE TABLE IF NOT EXISTS stock_counts(
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  item_id TEXT,
  qty INTEGER,
  ts TEXT DEFAULT (datetime('now','localtime')),
  note TEXT
);
"""

def init_db():
    is_new = not os.path.exists(DB)
    with sqlite3.connect(DB) as conn:
        conn.executescript(SCHEMA)
        if is_new:
            # 小學範例資料
            students_data = [
                ("1-01", "王小明"), ("1-02", "李小華"), ("1-03", "張小美"),
                ("1-04", "陳小強"), ("1-05", "林小芳")
            ]
            assignments_data = [
                ("數學習作P25", "數學習作第25頁", "2025-09-10"),
                ("國語作業簿", "國語作業簿第10課", "2025-09-12"),
                ("自然觀察日記", "自然觀察日記", "2025-09-15")
            ]
            items_data = [
                ("2B鉛筆", "2B 鉛筆"), ("橡皮擦", "橡皮擦"), ("尺", "30公分直尺")
            ]

            for student_id, name in students_data:
                conn.execute("INSERT OR IGNORE INTO students(id,name) VALUES(?,?)", (student_id, name))
            for assignment_id, title, due_date in assignments_data:
                conn.execute("INSERT OR IGNORE INTO assignments(id,title,due_date) VALUES(?,?,?)", (assignment_id, title, due_date))
            for item_id, name in items_data:
                conn.execute("INSERT OR IGNORE INTO items(id,name) VALUES(?,?)", (item_id, name))
            conn.commit()

def parse_payload(s: str):
    s = (s or "").strip().replace("｜","|")
    if not s or "|" not in s:
        return ("UNKNOWN", s)

    parts = s.split("|")
    tag = parts[0].upper()

    if tag == "HW" and len(parts) >= 3:
        student_id, assignment_id = parts[1], parts[2]
        return ("HW", {"student_id": student_id, "assignment_id": assignment_id})
    elif tag == "INV" and len(parts) >= 2:
        item_id = parts[1]
        qty = 1
        if len(parts) >= 3 and re.fullmatch(r"-?\d+", parts[2]):
            qty = int(parts[2])
        return ("INV", {"item_id": item_id, "qty": qty})
    else:
        return ("UNKNOWN", s)

@app.route("/")
def root():
    return send_from_directory("static", "index.html")

@app.post("/api/scan")
def api_scan():
    data = (request.json or {}).get("data", "")
    typ, payload = parse_payload(data)
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    with sqlite3.connect(DB) as conn:
        if typ == "HW":
            conn.execute("""
                INSERT OR IGNORE INTO submissions(student_id, assignment_id, status)
                VALUES (?, ?, 'submitted')
            """, (payload["student_id"], payload["assignment_id"]))
            conn.commit()
            return jsonify({
                "ok": True,
                "type": "HW",
                "message": f"作業登記 OK：{payload['student_id']} → {payload['assignment_id']} @ {now}",
            })
        elif typ == "INV":
            conn.execute("""
                INSERT INTO stock_counts(item_id, qty) VALUES (?, ?)
            """, (payload["item_id"], payload["qty"]))
            conn.commit()
            return jsonify({
                "ok": True,
                "type": "INV",
                "message": f"盤點記錄 OK：{payload['item_id']} × {payload['qty']} @ {now}",
            })
        else:
            return jsonify({"ok": False, "type": "UNKNOWN", "message": f"未識別內容：{payload}"}), 400

@app.get("/api/stats/submissions_today")
def stat_submissions_today():
    with sqlite3.connect(DB) as conn:
        cur = conn.execute("""
            SELECT student_id, assignment_id, ts
            FROM submissions
            WHERE date(ts) = date('now','localtime')
            ORDER BY ts DESC
        """)
        rows = [dict(student_id=s, assignment_id=a, ts=t) for (s,a,t) in cur.fetchall()]
        return jsonify(rows)

@app.get("/api/stats/stock_summary")
def stat_stock_summary():
    with sqlite3.connect(DB) as conn:
        cur = conn.execute("""
            SELECT item_id, COALESCE(SUM(qty),0) AS total_qty
            FROM stock_counts
            GROUP BY item_id
            ORDER BY item_id
        """)
        rows = [dict(item_id=i, total_qty=q) for (i,q) in cur.fetchall()]
        return jsonify(rows)

# 新增：未繳交清單（小學老師最需要的功能）
@app.get("/api/stats/missing_submissions")
def stat_missing_submissions():
    with sqlite3.connect(DB) as conn:
        cur = conn.execute("""
            SELECT s.id as student_id, s.name as student_name,
                   a.id as assignment_id, a.title as assignment_title
            FROM students s
            CROSS JOIN assignments a
            LEFT JOIN submissions sub ON (s.id = sub.student_id AND a.id = sub.assignment_id
                                        AND sub.day = date('now','localtime'))
            WHERE sub.id IS NULL
            ORDER BY s.id, a.id
        """)
        rows = [dict(student_id=sid, student_name=sname, assignment_id=aid, assignment_title=atitle)
                for (sid,sname,aid,atitle) in cur.fetchall()]
        return jsonify(rows)

# 新增：班級繳交統計
@app.get("/api/stats/class_summary")
def stat_class_summary():
    with sqlite3.connect(DB) as conn:
        cur = conn.execute("""
            SELECT a.id as assignment_id, a.title as assignment_title,
                   COUNT(DISTINCT s.id) as total_students,
                   COUNT(DISTINCT sub.student_id) as submitted_count,
                   (COUNT(DISTINCT s.id) - COUNT(DISTINCT sub.student_id)) as missing_count
            FROM assignments a
            CROSS JOIN students s
            LEFT JOIN submissions sub ON (a.id = sub.assignment_id AND s.id = sub.student_id
                                        AND sub.day = date('now','localtime'))
            GROUP BY a.id, a.title
            ORDER BY a.id
        """)
        rows = [dict(assignment_id=aid, assignment_title=atitle, total_students=total,
                    submitted_count=submitted, missing_count=missing)
                for (aid,atitle,total,submitted,missing) in cur.fetchall()]
        return jsonify(rows)

# AI 整合的 QR 碼生成功能
@app.post("/api/ai/generate-qr")
def ai_generate_qr():
    """使用 AI 智能生成 QR 碼"""
    data = request.json or {}
    prompt = data.get("prompt", "")
    qr_type = data.get("type", "auto")  # auto, student, assignment, full

    if not prompt:
        return jsonify({"ok": False, "message": "請提供生成提示"}), 400

    try:
        # 使用 AI 解析提示並生成 QR 碼內容
        qr_content = ai_parse_prompt(prompt, qr_type)

        if not qr_content:
            return jsonify({"ok": False, "message": "無法解析您的提示，請嘗試更具體的描述"}), 400

        # 生成 QR 碼圖片
        qr_image_base64 = generate_qr_image(qr_content)

        # 判斷使用的解析方式
        ai_method = "智能規則引擎"  # 預設使用規則引擎

        return jsonify({
            "ok": True,
            "content": qr_content,
            "image": qr_image_base64,
            "message": f"✅ {ai_method}成功生成 QR 碼：{qr_content}",
            "method": ai_method
        })

    except Exception as e:
        print(f"AI 生成錯誤：{e}")
        return jsonify({"ok": False, "message": f"生成失敗：{str(e)}"}), 500

def ai_parse_prompt(prompt, qr_type):
    """使用智能規則引擎解析用戶提示，生成 QR 碼內容"""

    # 使用內建智能規則引擎（不依賴外部 AI API）
    return _smart_rule_engine(prompt, qr_type)

def _smart_rule_engine(prompt, qr_type):
    """智能規則引擎 - 不依賴外部 AI API 的本地智能解析"""
    import re

    # 預處理：統一格式，提取關鍵資訊
    prompt = prompt.strip()
    prompt_lower = prompt.lower()

    # 數字提取模式
    numbers = re.findall(r'\d+', prompt)

    # 關鍵詞檢測
    keywords = {
        'student': ['學生', '座號', '學號', '同學'],
        'assignment': ['作業', '習作', '練習', '功課', '作業簿', '練習本'],
        'homework': ['繳交', '交作業', '提交', 'hw'],
        'inventory': ['盤點', '庫存', '清點', 'inv'],
        'subjects': {
            '數學': ['數學', 'math'],
            '國語': ['國語', '中文', '語文'],
            '英語': ['英語', '英文', 'english'],
            '自然': ['自然', '科學'],
            '社會': ['社會', '歷史', '地理'],
            '美勞': ['美勞', '美術', '勞作'],
            '體育': ['體育', '運動'],
            '音樂': ['音樂']
        },
        'items': {
            '鉛筆': ['鉛筆', '筆'],
            '橡皮擦': ['橡皮擦', '擦子'],
            '尺': ['尺', '直尺'],
            '彩色筆': ['彩色筆', '色筆']
        }
    }

    print(f"🤖 智能解析：{prompt}")  # 調試資訊

    # 智能分析邏輯
    def detect_intent():
        """檢測用戶意圖"""
        if any(word in prompt_lower for word in keywords['homework']):
            return 'homework'
        elif any(word in prompt_lower for word in keywords['student']):
            return 'student'
        elif any(word in prompt_lower for word in keywords['assignment']):
            return 'assignment'
        elif any(word in prompt_lower for word in keywords['inventory']):
            return 'inventory'
        elif qr_type != 'auto':
            return qr_type
        else:
            return 'unknown'

    def extract_class_and_seat():
        """提取班級和座號"""
        class_num = "1"
        seat_num = "1"

        # 尋找班級資訊
        class_patterns = [
            r'(\d+)[年班級]',
            r'(\d+)年',
            r'班級.*?(\d+)',
            r'第(\d+)班'
        ]
        for pattern in class_patterns:
            match = re.search(pattern, prompt)
            if match:
                class_num = match.group(1)
                break

        # 尋找座號資訊
        seat_patterns = [
            r'(\d+)[號座]',
            r'座號.*?(\d+)',
            r'第(\d+)號',
            r'(\d+)號同學'
        ]
        for pattern in seat_patterns:
            match = re.search(pattern, prompt)
            if match:
                seat_num = match.group(1)
                break

        # 如果沒有明確的班級座號，使用數字順序
        if numbers and len(numbers) >= 2:
            class_num = numbers[0]
            seat_num = numbers[1]
        elif numbers and len(numbers) == 1:
            seat_num = numbers[0]

        return class_num, seat_num

    def extract_subject_and_assignment():
        """提取科目和作業資訊"""
        subject = "作業"
        assignment_detail = ""

        # 檢測科目
        for subj, patterns in keywords['subjects'].items():
            if any(pattern in prompt_lower for pattern in patterns):
                subject = subj
                break

        # 檢測作業詳情
        if "習作" in prompt_lower:
            page_match = re.search(r'[pP]?(\d+)', prompt)
            if page_match:
                assignment_detail = f"習作P{page_match.group(1)}"
            else:
                assignment_detail = "習作"
        elif "作業簿" in prompt_lower:
            assignment_detail = "作業簿"
        elif "練習本" in prompt_lower:
            assignment_detail = "練習本"
        else:
            assignment_detail = "作業"

        return f"{subject}{assignment_detail}"

    def extract_item_and_quantity():
        """提取物品和數量"""
        item = "物品"
        quantity = "1"

        # 檢測物品
        for item_name, patterns in keywords['items'].items():
            if any(pattern in prompt_lower for pattern in patterns):
                item = item_name
                break

        # 檢測數量
        qty_patterns = [
            r'(\d+)[個件支張條本]',
            r'數量.*?(\d+)',
            r'共.*?(\d+)',
            r'總共.*?(\d+)'
        ]
        for pattern in qty_patterns:
            match = re.search(pattern, prompt)
            if match:
                quantity = match.group(1)
                break

        return item, quantity

    # 根據意圖生成對應內容
    intent = detect_intent()
    print(f"🎯 檢測意圖：{intent}")

    if intent == 'student':
        class_num, seat_num = extract_class_and_seat()
        result = f"{class_num}-{seat_num.zfill(2)}"
        print(f"👤 學生座號：{result}")
        return result

    elif intent == 'assignment':
        assignment = extract_subject_and_assignment()
        print(f"📚 作業名稱：{assignment}")
        return assignment

    elif intent == 'homework':
        class_num, seat_num = extract_class_and_seat()
        assignment = extract_subject_and_assignment()
        student_id = f"{class_num}-{seat_num.zfill(2)}"
        result = f"HW|{student_id}|{assignment}"
        print(f"✅ 作業繳交：{result}")
        return result

    elif intent == 'inventory':
        item, quantity = extract_item_and_quantity()
        result = f"INV|{item}|{quantity}"
        print(f"📦 庫存盤點：{result}")
        return result

    # 學生座號識別（備用邏輯）
    elif "學生" in prompt or "座號" in prompt or qr_type == "student":
        class_match = re.search(r'(\d+)[年班級]?', prompt)
        seat_match = re.search(r'(\d+)[號座]', prompt)

        class_num = class_match.group(1) if class_match else "1"
        seat_num = seat_match.group(1) if seat_match else "1"

        return f"{class_num}-{seat_num.zfill(2)}"

    # 作業識別
    elif "作業" in prompt or "習作" in prompt or qr_type == "assignment":
        if "數學" in prompt:
            page_match = re.search(r'(\d+)', prompt)
            page = page_match.group(1) if page_match else "25"
            return f"數學習作P{page}"
        elif "國語" in prompt:
            return "國語作業簿"
        elif "英語" in prompt or "英文" in prompt:
            return "英語練習本"
        else:
            words = prompt.split()
            return words[0] if words else "作業"

    # 完整格式識別
    elif "繳交" in prompt or "hw" in prompt_lower or qr_type == "full":
        class_match = re.search(r'(\d+)[年班級]?', prompt)
        seat_match = re.search(r'(\d+)[號座]', prompt)

        class_num = class_match.group(1) if class_match else "1"
        seat_num = seat_match.group(1) if seat_match else "1"
        student_id = f"{class_num}-{seat_num.zfill(2)}"

        if "數學" in prompt:
            page_match = re.search(r'(\d+)', prompt)
            page = page_match.group(1) if page_match else "25"
            assignment = f"數學習作P{page}"
        elif "國語" in prompt:
            assignment = "國語作業簿"
        elif "英語" in prompt or "英文" in prompt:
            assignment = "英語練習本"
        else:
            assignment = "作業"

        return f"HW|{student_id}|{assignment}"

    # 庫存盤點識別
    elif "盤點" in prompt or "庫存" in prompt or "inv" in prompt_lower:
        qty_match = re.search(r'(\d+)[個件支張]', prompt)
        qty = qty_match.group(1) if qty_match else "1"

        if "鉛筆" in prompt:
            return f"INV|2B鉛筆|{qty}"
        elif "橡皮擦" in prompt:
            return f"INV|橡皮擦|{qty}"
        else:
            return f"INV|物品|{qty}"

    # 直接返回原始內容（如果已經是正確格式）
    elif "|" in prompt:
        return prompt

    # 默認處理
    else:
        return prompt

def generate_qr_image(content):
    """生成 QR 碼圖片並返回 base64 編碼"""
    try:
        # 創建 QR 碼
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(content)
        qr.make(fit=True)

        # 生成圖片
        img = qr.make_image(fill_color="black", back_color="white")

        # 轉換為 base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode()

        return f"data:image/png;base64,{img_base64}"

    except Exception as e:
        print(f"QR 碼生成錯誤：{e}")
        return None

if __name__ == "__main__":
    init_db()
    # 對外可連：0.0.0.0；手機在同一個 Wi-Fi 可連到你的電腦 IP
    # 使用 9000 埠避免埠號衝突
    # 使用 ssl_context='adhoc' 啟用 HTTPS（需要安裝 pyOpenSSL）
    try:
        # 嘗試使用 HTTPS
        print("🔐 啟動 HTTPS 模式...")
        app.run(host="0.0.0.0", port=9000, debug=True, ssl_context='adhoc')
    except ImportError:
        print("⚠️  警告：無法啟用 HTTPS，某些手機可能無法使用相機")
        print("💡 建議安裝：pip install pyOpenSSL cryptography")
        print("🔄 使用 HTTP 模式啟動...")
        app.run(host="0.0.0.0", port=9000, debug=True)
